package com.teammanage.exception;

/**
 * 团队成员被禁用异常
 * 当用户在团队中的账户状态为"已禁用"或"已停用"时抛出此异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TeamMemberDisabledException extends RuntimeException {

    private final Integer code;
    private final Long teamId;
    private final Long userId;

    public TeamMemberDisabledException(String message) {
        super(message);
        this.code = 403;
        this.teamId = null;
        this.userId = null;
    }

    public TeamMemberDisabledException(String message, Long teamId, Long userId) {
        super(message);
        this.code = 403;
        this.teamId = teamId;
        this.userId = userId;
    }

    public TeamMemberDisabledException(Integer code, String message) {
        super(message);
        this.code = code;
        this.teamId = null;
        this.userId = null;
    }

    public TeamMemberDisabledException(String message, Throwable cause) {
        super(message, cause);
        this.code = 403;
        this.teamId = null;
        this.userId = null;
    }

    public Integer getCode() {
        return code;
    }

    public Long getTeamId() {
        return teamId;
    }

    public Long getUserId() {
        return userId;
    }

    /**
     * 创建用户在团队中被禁用的异常
     */
    public static TeamMemberDisabledException userDisabledInTeam(Long teamId, Long userId) {
        return new TeamMemberDisabledException("您的账户已在此团队中被禁用", teamId, userId);
    }

    /**
     * 创建用户在团队中被停用的异常
     */
    public static TeamMemberDisabledException userDeactivatedInTeam(Long teamId, Long userId) {
        return new TeamMemberDisabledException("当前账户在该团队中已被停用", teamId, userId);
    }

    /**
     * 创建通用的成员状态异常
     */
    public static TeamMemberDisabledException memberStatusInactive(Long teamId, Long userId) {
        return new TeamMemberDisabledException("您的账户在此团队中处于非激活状态，无法访问团队", teamId, userId);
    }
}
