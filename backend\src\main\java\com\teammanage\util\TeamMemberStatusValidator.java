package com.teammanage.util;

import com.teammanage.entity.TeamMember;
import com.teammanage.exception.TeamMemberDisabledException;
import com.teammanage.mapper.TeamMemberMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 团队成员状态验证工具类
 * 提供团队成员状态检查和验证的通用方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class TeamMemberStatusValidator {

    private static final Logger log = LoggerFactory.getLogger(TeamMemberStatusValidator.class);

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    /**
     * 验证用户是否为团队的激活成员
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @throws TeamMemberDisabledException 如果用户不是团队成员或状态为非激活
     */
    public void validateActiveMember(Long teamId, Long userId) {
        TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        
        if (teamMember == null || teamMember.getIsDeleted()) {
            log.warn("用户不是团队成员: teamId={}, userId={}", teamId, userId);
            throw TeamMemberDisabledException.memberStatusInactive(teamId, userId);
        }
        
        if (!Boolean.TRUE.equals(teamMember.getIsActive())) {
            log.warn("团队成员状态为非激活: teamId={}, userId={}, isActive={}", 
                    teamId, userId, teamMember.getIsActive());
            throw TeamMemberDisabledException.memberStatusInactive(teamId, userId);
        }
        
        log.debug("团队成员状态验证通过: teamId={}, userId={}", teamId, userId);
    }

    /**
     * 检查用户是否为团队的激活成员（不抛出异常）
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 是否为激活成员
     */
    public boolean isActiveMember(Long teamId, Long userId) {
        try {
            validateActiveMember(teamId, userId);
            return true;
        } catch (TeamMemberDisabledException e) {
            return false;
        }
    }

    /**
     * 获取团队成员信息并验证状态
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 团队成员信息
     * @throws TeamMemberDisabledException 如果用户不是团队成员或状态为非激活
     */
    public TeamMember getActiveMember(Long teamId, Long userId) {
        TeamMember teamMember = teamMemberMapper.findByTeamIdAndAccountId(teamId, userId);
        
        if (teamMember == null || teamMember.getIsDeleted()) {
            log.warn("用户不是团队成员: teamId={}, userId={}", teamId, userId);
            throw TeamMemberDisabledException.memberStatusInactive(teamId, userId);
        }
        
        if (!Boolean.TRUE.equals(teamMember.getIsActive())) {
            log.warn("团队成员状态为非激活: teamId={}, userId={}, isActive={}", 
                    teamId, userId, teamMember.getIsActive());
            throw TeamMemberDisabledException.memberStatusInactive(teamId, userId);
        }
        
        return teamMember;
    }

    /**
     * 验证团队成员对象的状态
     * 
     * @param teamMember 团队成员对象
     * @throws TeamMemberDisabledException 如果成员状态为非激活
     */
    public void validateMemberStatus(TeamMember teamMember) {
        if (teamMember == null) {
            throw new IllegalArgumentException("团队成员对象不能为空");
        }
        
        if (teamMember.getIsDeleted()) {
            log.warn("团队成员已被删除: teamId={}, userId={}", 
                    teamMember.getTeamId(), teamMember.getAccountId());
            throw TeamMemberDisabledException.memberStatusInactive(
                    teamMember.getTeamId(), teamMember.getAccountId());
        }
        
        if (!Boolean.TRUE.equals(teamMember.getIsActive())) {
            log.warn("团队成员状态为非激活: teamId={}, userId={}, isActive={}", 
                    teamMember.getTeamId(), teamMember.getAccountId(), teamMember.getIsActive());
            throw TeamMemberDisabledException.memberStatusInactive(
                    teamMember.getTeamId(), teamMember.getAccountId());
        }
    }

    /**
     * 检查团队成员对象的状态（不抛出异常）
     * 
     * @param teamMember 团队成员对象
     * @return 是否为激活状态
     */
    public boolean isMemberActive(TeamMember teamMember) {
        if (teamMember == null || teamMember.getIsDeleted()) {
            return false;
        }
        return Boolean.TRUE.equals(teamMember.getIsActive());
    }
}
